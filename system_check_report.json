{"timestamp": "2025-05-25 22:25:34", "overall_status": "失败", "results": {"database": {"status": "错误", "details": {"error": "invalid dsn: invalid connection option \"sqlite_path\"\n"}}, "modules": {"status": "通过", "details": {"email_module.email_connection": {"status": "通过", "details": "模块可导入"}, "email_module.email_filter": {"status": "通过", "details": "模块可导入"}, "email_module.email_type_analyzer": {"status": "通过", "details": "模块可导入"}, "ai.adapter": {"status": "通过", "details": "模块可导入"}}}, "integration": {"status": "通过", "details": {"email_filter": {"status": "通过", "details": "EmailFilter正确集成EmailTypeAnalyzer"}, "email_type_analyzer": {"status": "通过", "details": "EmailTypeAnalyzer正确集成AIAdapter"}}}, "workflow": {"status": "通过", "details": {"scheduled_tasks_exists": true, "batch_analyze_exists": true, "bat_exists": true}}}}